{"name": "workly", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:expo-go": "node scripts/switch-expo-go.js expo-go && expo start --clear", "start:normal": "node scripts/switch-expo-go.js normal && expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "ios:expo-go": "npm run start:expo-go && echo 'Press i to open iOS Simulator'", "android:expo-go": "npm run start:expo-go && echo 'Press a to open Android Emulator'", "dev:ios": "npm run start:normal && npx expo start --dev-client --ios", "dev:android": "npm run start:normal && npx expo start --dev-client --android", "web": "expo start --web", "generate-icons": "node scripts/generate-icons.js", "icons": "npm run generate-icons", "switch:expo-go": "node scripts/switch-expo-go.js expo-go", "switch:normal": "node scripts/switch-expo-go.js normal"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "date-fns": "^4.1.0", "expo": "53.0.12", "expo-audio": "~0.4.6", "expo-av": "~15.1.6", "expo-constants": "^17.1.6", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.5", "expo-location": "^18.1.5", "expo-notifications": "~0.31.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest-environment-jsdom": "^30.0.2", "typescript": "~5.8.3"}, "private": true, "keywords": ["workly", "shift-management", "attendance", "work-schedule"], "author": "Workly Team", "license": "MIT", "description": "Ứng dụng quản lý ca làm việc cá nhân với tính năng chấm công, nhắc nhở và thống kê"}